<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #C5E4FD 0%, #E8F4FD 100%);
            padding: 40px 20px;
            min-height: 100vh;
        }

        .warranty-process-section {
            max-width: 1000px;
            margin: 0 auto;
        }

        .process-title {
            text-align: center;
            background-color: #2083D0;
            color: white;
            padding: 12px 30px;
            border-radius: 13px;
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 40px;
            display: inline-block;
            width: auto;
            margin-left: 50%;
            transform: translateX(-50%);
        }

        .process-step {
            margin-bottom: 50px;
        }

        .step-title {
            font-size: 22px;
            color: #2083D0;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .step-description {
            font-size: 18px;
            color: #055EA3;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .step-options {
            display: flex;
            justify-content: space-between;
            gap: 30px;
            margin-bottom: 30px;
        }

        .option-item {
            flex: 1;
            text-align: center;
            position: relative;
        }

        .option-item:not(:last-child)::after {
            content: '';
            position: absolute;
            right: -15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #2083D0;
        }

        .option-icon {
            margin: 0 auto 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .option-icon img {
            width: 113px;
            height: 113px;
        }

        .option-title {
            font-size: 18px;
            color: #2083D0;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .step-flow {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
            margin-bottom: 30px;
        }

        .flow-item {
            flex: 1;
            text-align: center;
        }

        .flow-icon {
            margin: 0 auto 24px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .flow-icon img {
            width: 113px;
            height: 113px;
        }

        .flow-title {
            font-size: 18px;
            color: #2083D0;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .flow-subtitle {
            font-size: 18px;
            color: #055EA3;
            line-height: 1.4;
        }

        .flow-arrow {
            font-size: 30px;
            color: #2083D0;
            font-weight: bold;
        }

        .step-services {
            display: flex;
            justify-content: space-between;
            gap: 30px;
            margin-bottom: 30px;
        }

        .service-item {
            flex: 1;
            text-align: center;
            position: relative;
        }

        .service-item:not(:last-child)::after {
            content: '';
            position: absolute;
            right: -15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #2083D0;
        }

        .service-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
            background-color: #2083D0;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .service-icon img {
            width: 50px;
            height: 50px;
        }

        .service-title {
            font-size: 18px;
            color: #2083D0;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .service-description {
            font-size: 18px;
            color: #055EA3;
            line-height: 1.4;
        }

        .divider {
            height: 2px;
            background-color: #2083D0;
            margin: 40px 0;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .step-options,
            .step-flow,
            .step-services {
                flex-direction: column;
                gap: 30px;
            }

            .option-item:not(:last-child)::after,
            .service-item:not(:last-child)::after {
                display: none;
            }

            .flow-arrow {
                transform: rotate(90deg);
                margin: 10px 0;
            }
        }

        @media (max-width: 480px) {
            .process-title {
                font-size: 20px;
            }

            .step-title {
                font-size: 20px;
            }

            .step-description,
            .option-title,
            .flow-title,
            .flow-subtitle,
            .service-title,
            .service-description {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="warranty-process-section">
        <div class="process-title">Quy Trình Bảo Hành</div>

        <!-- Step 1 -->
        <div class="process-step">
            <h2 class="step-title">Bước 1: Liên hệ và thông báo tình trạng</h2>
            <p class="step-description">
                Ngay khi phát hiện bất kỳ dấu hiệu bất thường nào với răng Implant của mình (ví dụ: đau nhức, lung lay, sưng nướu, nứt vỡ răng sứ...), bạn vui lòng liên hệ ngay lập tức với nha khoa qua các kênh sau:
            </p>

            <div class="step-options">
                <div class="option-item">
                    <div class="option-icon">
                        <img src="https://nhakhoaquoctesg.vn/wp-content/uploads/2025/07/telesale-icon-1.png" alt="Sale icon">
                    </div>
                    <div class="option-title">Hotline</div>
                </div>

                <div class="option-item">
                    <div class="option-icon">
                        <img src="https://nhakhoaquoctesg.vn/wp-content/uploads/2025/07/zalo-icon-1.png" alt="Zalo icon">
                    </div>
                    <div class="option-title">Zalo Official Account</div>
                </div>

                <div class="option-item">
                    <div class="option-icon">
                        <img src="https://nhakhoaquoctesg.vn/wp-content/uploads/2025/07/zalo-icon-1.png" alt="Zalo icon">
                    </div>
                    <div class="option-title">Fanpage Facebook</div>
                </div>
            </div>
        </div>

        <div class="divider"></div>

        <!-- Step 2 -->
        <div class="process-step">
            <h2 class="step-title">Bước 2: Đặt Lịch Hẹn Thăm Khám</h2>
            <p class="step-description">
                Sau khi tiếp nhận thông tin, bộ phận Chăm sóc khách hàng của chúng tôi sẽ nhanh chóng sắp xếp một lịch hẹn sớm nhất và thuận tiện nhất cho bạn để được gặp trực tiếp bác sĩ chuyên khoa.
            </p>

            <div class="step-flow">
                <div class="flow-item">
                    <div class="flow-icon">
                        <img src="https://nhakhoaquoctesg.vn/wp-content/uploads/2025/07/telesale-icon-1.png" alt="Sale icon">
                    </div>
                    <div class="flow-title">Tiếp nhận thông tin</div>
                </div>

                <div class="flow-arrow">→</div>

                <div class="flow-item">
                    <div class="flow-icon">
                        <img src="https://nhakhoaquoctesg.vn/wp-content/uploads/2025/07/zalo-icon-1.png" alt="Zalo icon">
                    </div>
                    <div class="flow-title">Sắp xếp lịch hẹn</div>
                </div>

                <div class="flow-arrow">→</div>

                <div class="flow-item">
                    <div class="flow-icon">
                        <img src="https://nhakhoaquoctesg.vn/wp-content/uploads/2025/07/facebook-icon.png" alt="facebook icon">
                    </div>
                    <div class="flow-title">Gặp trực tiếp<br> bác sĩ chuyên khoa</div>
                </div>
            </div>
        </div>

        <div class="divider"></div>

        <!-- Step 3 -->
        <div class="process-step">
            <h2 class="step-title">Bước 3: Bác Sĩ Chuyên Môn Thăm Khám & Chẩn Đoán</h2>
            <p class="step-description">
                Tại buổi hẹn, bạn sẽ được bác sĩ chuyên khoa Implant trực tiếp thăm khám. Quá trình này bao gồm:
            </p>

            <div class="step-services">
                <div class="service-item">
                    <div class="service-icon">
                        <svg width="50" height="50" viewBox="0 0 24 24" fill="white">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="m21 21-4.35-4.35"/>
                            <circle cx="11" cy="11" r="3"/>
                        </svg>
                    </div>
                    <div class="service-title">Chụp phim kiểm tra</div>
                    <div class="service-description">
                        Chụp lại phim X-quang hoặc CT Cone Beam (nếu cần) để đánh giá chính xác tình trạng của trụ Implant và xương hàm bên dưới.
                    </div>
                </div>

                <div class="service-item">
                    <div class="service-icon">
                        <svg width="50" height="50" viewBox="0 0 24 24" fill="white">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14,2 14,8 20,8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10,9 9,9 8,9"/>
                        </svg>
                    </div>
                    <div class="service-title">Chẩn đoán nguyên nhân</div>
                    <div class="service-description">
                        Dựa trên kết quả thăm khám và phim chụp, bác sĩ sẽ xác định chính xác nguyên nhân gây ra vấn đề.
                    </div>
                </div>

                <div class="service-item">
                    <div class="service-icon">
                        <svg width="50" height="50" viewBox="0 0 24 24" fill="white">
                            <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                        </svg>
                    </div>
                    <div class="service-title">Thăm khám lâm sàng</div>
                    <div class="service-description">
                        Kiểm tra tình trạng răng Implant, nướu và các mô xung quanh.
                    </div>
                </div>
            </div>
        </div>

        <div class="divider"></div>

        <!-- Step 4 -->
        <div class="process-step">
            <h2 class="step-title">Bước 4: Xác Nhận Tình Trạng Bảo Hành và Tư Vấn Hướng Xử Lý</h2>
            <p class="step-description">
                Sau khi thăm khám, các bác sĩ sẽ xác định nguyên nhân cụ thể và đưa ra hướng giải quyết chi tiết:
            </p>
        </div>
    </div>
</body>
</html>